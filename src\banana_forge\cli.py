"""
Banana Forge CLI - AI-powered feature implementation plan generator.

This module provides the command-line interface for Banana Forge, allowing users
to generate structured feature implementation plans using AI agents.
"""

from pathlib import Path

import typer

from .config import settings
from .core import run_generation
from .vector_store import get_vector_store

app = typer.Typer(
    name="banana-forge",
    help="AI-powered CLI tool for generating structured feature implementation plans",
    add_completion=False,
)


@app.command()
def generate(
    feature: str = typer.Argument(
        ..., help="Feature description or name to generate implementation plan for"
    ),
    input_file: Path | None = typer.Option(
        None,
        "--input",
        "-i",
        help="Optional input file with additional context or specifications",
        exists=True,
        file_okay=True,
        dir_okay=False,
    ),
    output_file: Path | None = typer.Option(
        None,
        "--output",
        "-o",
        help="Optional output file to save the generated plan (default: stdout)",
        file_okay=True,
        dir_okay=False,
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable verbose logging to see detailed progress",
    ),
    dry_run: bool = typer.Option(
        False,
        "--dry-run",
        help="Show what would be done without actually calling AI models",
    ),
) -> None:
    """
    Generate a structured feature implementation plan using AI agents.

    This command takes a feature description and produces a comprehensive
    Markdown report following the 10-agent template structure.

    Examples:
        banana-forge generate "OAuth login system"
        banana-forge generate "Real-time chat feature" --verbose
        banana-forge generate "Payment integration" -o payment_plan.md
    """
    try:
        # Set verbose mode in settings if requested
        if verbose:
            settings.verbose = True
            typer.echo("Verbose mode enabled", err=True)

        if dry_run:
            typer.echo("DRY RUN MODE - No AI models will be called", err=True)

        # Read additional context from input file if provided
        additional_context = ""
        if input_file:
            try:
                additional_context = input_file.read_text(encoding="utf-8")
                if verbose:
                    typer.echo(f"Loaded additional context from {input_file}", err=True)
            except Exception as e:
                typer.echo(f"Error reading input file: {e}", err=True)
                raise typer.Exit(1)

        # Generate the implementation plan
        if verbose:
            typer.echo(f"Generating implementation plan for: {feature}", err=True)

        result = run_generation(
            feature_description=feature,
            additional_context=additional_context,
            dry_run=dry_run,
        )

        # Output the result
        if output_file:
            try:
                output_file.write_text(result, encoding="utf-8")
                typer.echo(f"Plan saved to {output_file}", err=True)
            except Exception as e:
                typer.echo(f"Error writing output file: {e}", err=True)
                raise typer.Exit(1)
        else:
            typer.echo(result)

        if verbose:
            typer.echo("Generation completed successfully", err=True)

    except KeyboardInterrupt:
        typer.echo("\nOperation cancelled by user", err=True)
        raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error: {e}", err=True)
        if verbose:
            import traceback

            typer.echo(traceback.format_exc(), err=True)
        raise typer.Exit(1)


@app.command()
def version() -> None:
    """Show the version of Banana Forge."""
    from . import __version__

    typer.echo(f"Banana Forge v{__version__}")


@app.command()
def config() -> None:
    """Show current configuration settings."""
    typer.echo("Current Banana Forge Configuration:")
    typer.echo(
        f"  OpenRouter API Key: {'Set' if settings.openrouter_api_key else 'Not set'}"
    )
    typer.echo(f"  Ollama Base URL: {settings.ollama_base_url}")
    typer.echo(f"  Primary Model: {settings.primary_model}")
    typer.echo(f"  Local Model: {settings.local_model}")
    typer.echo(f"  Max Concurrent Agents: {settings.max_concurrent_agents}")
    typer.echo(f"  ChromaDB Path: {settings.chroma_db_path}")
    typer.echo(f"  Log Level: {settings.log_level}")


@app.command()
def index(
    path: Path | None = typer.Option(
        None,
        "--path",
        "-p",
        help="Path to index (defaults to current project)",
        exists=True,
        file_okay=False,
        dir_okay=True,
    ),
    reset: bool = typer.Option(
        False,
        "--reset",
        help="Reset the collection before indexing",
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable verbose output",
    ),
) -> None:
    """Index project files into ChromaDB for semantic search."""
    try:
        if verbose:
            settings.verbose = True
            typer.echo("Verbose mode enabled", err=True)

        vector_store = get_vector_store()

        if reset:
            typer.echo("Resetting ChromaDB collection...", err=True)
            if vector_store.reset_collection():
                typer.echo("Collection reset successfully", err=True)
            else:
                typer.echo("Failed to reset collection", err=True)
                raise typer.Exit(1)

        typer.echo("Starting to index project files...", err=True)
        stats = vector_store.index_project_files(path)

        typer.echo("\nIndexing Results:", err=True)
        typer.echo(f"  Files processed: {stats['files_processed']}", err=True)
        typer.echo(f"  Files indexed: {stats['files_indexed']}", err=True)
        typer.echo(f"  Errors: {stats['errors']}", err=True)
        typer.echo(
            f"  Total documents in collection: {stats['collection_count']}", err=True
        )

        if stats["files_indexed"] > 0:
            typer.echo("✅ Indexing completed successfully!", err=True)
        else:
            typer.echo("⚠️  No new files were indexed", err=True)

    except Exception as e:
        typer.echo(f"Error during indexing: {e}", err=True)
        raise typer.Exit(1)


@app.command()
def search(
    query: str = typer.Argument(..., help="Search query to find relevant code"),
    limit: int = typer.Option(5, "--limit", "-l", help="Maximum number of results"),
    verbose: bool = typer.Option(
        False, "--verbose", "-v", help="Show detailed results"
    ),
) -> None:
    """Search the indexed codebase for relevant snippets."""
    try:
        if verbose:
            settings.verbose = True

        vector_store = get_vector_store()
        results = vector_store.search(query, n_results=limit)

        if not results:
            typer.echo("No results found.", err=True)
            return

        typer.echo(f"Found {len(results)} results for: {query}\n")

        for i, result in enumerate(results, 1):
            metadata = result.get("metadata", {})
            file_path = metadata.get("file_path", "unknown")
            file_type = metadata.get("file_type", "")

            typer.echo(f"[{i}] {file_path}{file_type}")
            if verbose and result.get("distance"):
                typer.echo(f"    Distance: {result['distance']:.4f}")

            # Show content preview
            content = result["content"]
            if len(content) > 200 and not verbose:
                content = content[:200] + "..."

            typer.echo(f"    {content}\n")

    except Exception as e:
        typer.echo(f"Search error: {e}", err=True)
        raise typer.Exit(1)


@app.command()
def db_stats() -> None:
    """Show ChromaDB collection statistics."""
    try:
        vector_store = get_vector_store()
        stats = vector_store.get_collection_stats()

        if "error" in stats:
            typer.echo(f"Error: {stats['error']}", err=True)
            raise typer.Exit(1)

        typer.echo("ChromaDB Statistics:")
        typer.echo(f"  Collection: {stats.get('collection_name', 'unknown')}")
        typer.echo(f"  Total documents: {stats.get('total_documents', 0)}")
        typer.echo(f"  Database path: {stats.get('database_path', 'unknown')}")

        file_types = stats.get("sample_file_types", {})
        if file_types:
            typer.echo("  Sample file types:")
            for file_type, count in file_types.items():
                typer.echo(f"    {file_type}: {count}")

    except Exception as e:
        typer.echo(f"Error getting stats: {e}", err=True)
        raise typer.Exit(1)


def main() -> None:
    """Main entry point for the CLI application."""
    app()


if __name__ == "__main__":
    main()
