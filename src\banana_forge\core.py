"""
Core functionality for Banana Forge feature plan generation.

This module contains the main logic for orchestrating AI agents to generate
comprehensive feature implementation plans.
"""

import logging
from datetime import datetime

from .config import settings, setup_logging
from .llm import LLMClient
from .templates import get_feature_template

logger = logging.getLogger(__name__)


def run_generation(
    feature_description: str,
    additional_context: str = "",
    dry_run: bool = False,
) -> str:
    """
    Generate a comprehensive feature implementation plan.
    
    This is the main entry point for the generation process. In Phase 1 (MVP),
    this will use a simple single-LLM approach. In Phase 2, this will be
    enhanced with multi-agent orchestration.
    
    Args:
        feature_description: The feature to generate a plan for
        additional_context: Optional additional context or specifications
        dry_run: If True, return a mock response without calling AI models
        
    Returns:
        str: The generated implementation plan in Markdown format
        
    Raises:
        ValueError: If required configuration is missing
        Exception: If generation fails
    """
    # Set up logging
    setup_logging()

    if settings.verbose:
        logger.info(f"Starting generation for feature: {feature_description}")

    # Validate configuration
    if not dry_run:
        try:
            settings.validate_required_settings()
        except ValueError as e:
            logger.error(f"Configuration validation failed: {e}")
            raise

    # Handle dry run mode
    if dry_run:
        return _generate_dry_run_response(feature_description, additional_context)

    try:
        # Initialize LLM client
        llm_client = LLMClient()

        # Phase 1 MVP: Simple single-model generation
        # In Phase 2, this will be replaced with multi-agent orchestration
        result = _generate_simple_plan(
            llm_client=llm_client,
            feature_description=feature_description,
            additional_context=additional_context,
        )

        if settings.verbose:
            logger.info("Generation completed successfully")

        return result

    except Exception as e:
        logger.error(f"Generation failed: {e}")
        if settings.verbose:
            import traceback
            logger.debug(traceback.format_exc())
        raise


def _generate_dry_run_response(feature_description: str, additional_context: str) -> str:
    """Generate a mock response for dry run mode."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    context_text = additional_context if additional_context else "No additional context provided."

    return f"""# {feature_description} Implementation Plan

*Generated on {timestamp}*

## Overview

[DRY RUN] This would provide a comprehensive overview of the {feature_description} feature, including:
- Purpose and business value
- High-level technical approach
- Key components and integrations
- Success criteria

{context_text}

## Current State Analysis

[DRY RUN] This would analyze the current codebase and identify:
- Relevant files, modules, and components
- Existing functionality that relates to this feature
- Potential conflicts or dependencies

## Reference Standards

[DRY RUN] This would gather relevant documentation and standards:
- Project-specific coding standards
- Library documentation and best practices
- Security and performance requirements

## Implementation Phases

[DRY RUN] This would outline implementation phases:
- Phase 1: Foundation setup
- Phase 2: Core functionality
- Phase 3: Integration and testing
- Phase 4: Deployment and monitoring

## Validation Criteria

[DRY RUN] This would define validation criteria:
- Functional requirements testing
- Performance benchmarks
- Security validation
- User acceptance criteria

## Risk Mitigation

[DRY RUN] This would identify risks and mitigations:
- Technical risks and solutions
- Project timeline risks
- Resource availability concerns

---
*Generated by Banana Forge on {timestamp}*"""


def _generate_simple_plan(
    llm_client: LLMClient,
    feature_description: str,
    additional_context: str,
) -> str:
    """
    Generate a simple implementation plan using a single LLM call.
    
    This is the Phase 1 MVP implementation. It will be enhanced in Phase 2
    with multi-agent orchestration and context gathering.
    """
    if settings.verbose:
        logger.info("Using simple single-model generation (Phase 1 MVP)")

    # Prepare the prompt
    prompt = _build_generation_prompt(feature_description, additional_context)

    if settings.verbose:
        logger.info(f"Calling primary model: {settings.primary_model}")

    # Call the LLM
    response = llm_client.generate_completion(
        prompt=prompt,
        model=settings.primary_model,
        max_tokens=8000,  # Allow for comprehensive output
        temperature=0.7,  # Balanced creativity and consistency
    )

    # Post-process the response
    result = _post_process_response(response, feature_description)

    return result


def _build_generation_prompt(feature_description: str, additional_context: str) -> str:
    """Build the prompt for the LLM generation."""
    template = get_feature_template()

    prompt = f"""You are an expert software architect and technical writer. Generate a comprehensive feature implementation plan following the exact structure provided below.

FEATURE TO IMPLEMENT: {feature_description}

ADDITIONAL CONTEXT:
{additional_context if additional_context else "No additional context provided."}

INSTRUCTIONS:
1. Follow the exact markdown structure provided in the template
2. Fill in each section with detailed, actionable content
3. Be specific and practical in your recommendations
4. Include relevant technical considerations
5. Ensure the plan is comprehensive but focused

TEMPLATE TO FOLLOW:
{template}

Generate the complete implementation plan now, following the template structure exactly:"""

    return prompt


def _post_process_response(response: str, feature_description: str) -> str:
    """Post-process the LLM response to ensure quality and formatting."""
    # Basic validation and cleanup
    if not response.strip():
        raise ValueError("Empty response from LLM")

    # Ensure the response starts with a proper heading
    if not response.strip().startswith("#"):
        response = f"# {feature_description} Implementation Plan\n\n{response}"

    # Add generation timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    footer = f"\n\n---\n*Generated by Banana Forge on {timestamp}*\n"

    return response + footer


# Phase 2 placeholder functions (to be implemented later)
def _setup_multi_agent_orchestration():
    """Set up the multi-agent research workflow (Phase 2)."""
    pass


def _gather_code_context():
    """Gather relevant code context using ChromaDB (Phase 2)."""
    pass


def _gather_documentation_context():
    """Gather relevant documentation using Context7 (Phase 2)."""
    pass


def _run_parallel_agents():
    """Run parallel research agents (Phase 2)."""
    pass


def _synthesize_final_report():
    """Synthesize the final report from agent outputs (Phase 2)."""
    pass
