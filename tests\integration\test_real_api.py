"""
Integration tests that use real API calls.

These tests are optional and only run when API keys are configured.
They validate the full pipeline with actual AI models.
"""

import os

import pytest

from banana_forge.config import settings
from banana_forge.core import run_generation
from banana_forge.llm import LLMClient


class TestRealAPIIntegration:
    """Integration tests using real API calls."""

    @pytest.fixture(autouse=True)
    def setup_api_key(self):
        """Skip tests if no API key is configured."""
        if not os.getenv("OPENROUTER_API_KEY"):
            pytest.skip(
                "OPENROUTER_API_KEY not configured - skipping integration tests"
            )

    def test_real_generation_simple_feature(self):
        """Test generating a plan for a simple feature using real API."""
        feature = "Simple user registration form"

        result = run_generation(
            feature_description=feature, additional_context="", dry_run=False
        )

        # Validate the result structure
        assert isinstance(result, str)
        assert len(result) > 100  # Should be substantial content
        assert feature in result or "registration" in result.lower()
        assert "# " in result  # Should have markdown headers
        assert "## " in result  # Should have section headers
        assert "Generated by Banana Forge" in result

    def test_real_generation_with_context(self):
        """Test generating a plan with additional context."""
        feature = "API rate limiting"
        context = "We use FastAPI framework and Redis for caching"

        result = run_generation(
            feature_description=feature, additional_context=context, dry_run=False
        )

        # Validate the result
        assert isinstance(result, str)
        assert len(result) > 200
        assert "rate limiting" in result.lower() or "rate" in result.lower()
        assert "Generated by Banana Forge" in result

    def test_llm_client_openrouter_health(self):
        """Test that OpenRouter client is healthy."""
        client = LLMClient()
        health = client.health_check()

        # OpenRouter should be available
        assert "openrouter" in health
        # Note: This might fail if API key is invalid or service is down
        # That's expected behavior for integration tests

    def test_llm_client_model_listing(self):
        """Test listing available models."""
        client = LLMClient()
        models = client.list_available_models()

        assert isinstance(models, dict)
        assert "openrouter" in models
        assert "ollama" in models

    @pytest.mark.slow
    def test_comprehensive_feature_generation(self):
        """Test generating a comprehensive plan for a complex feature."""
        feature = "Multi-tenant SaaS authentication system with OAuth2 and RBAC"
        context = """
        Requirements:
        - Support multiple identity providers (Google, GitHub, Microsoft)
        - Role-based access control with custom permissions
        - Multi-tenant architecture with data isolation
        - JWT tokens with refresh mechanism
        - Audit logging for security events
        """

        result = run_generation(
            feature_description=feature, additional_context=context, dry_run=False
        )

        # Validate comprehensive output
        assert isinstance(result, str)
        assert len(result) > 1000  # Should be very detailed

        # Check for key sections that should be present
        expected_sections = [
            "## Overview",
            "## Current State Analysis",
            "## Implementation",
            "## Validation",
            "## Risk",
        ]

        for section in expected_sections:
            assert section in result, f"Missing section: {section}"

        # Check for relevant technical terms
        technical_terms = ["oauth", "jwt", "rbac", "tenant", "authentication"]
        result_lower = result.lower()
        found_terms = [term for term in technical_terms if term in result_lower]
        assert len(found_terms) >= 3, (
            f"Expected technical terms not found. Found: {found_terms}"
        )

    def test_error_handling_invalid_model(self):
        """Test error handling with invalid model configuration."""
        # Temporarily change to invalid model
        original_model = settings.primary_model
        settings.primary_model = "invalid/nonexistent-model"

        try:
            with pytest.raises(Exception):
                run_generation(
                    feature_description="Test feature",
                    additional_context="",
                    dry_run=False,
                )
        finally:
            # Restore original model
            settings.primary_model = original_model

    @pytest.mark.parametrize(
        "feature_type",
        [
            "REST API endpoint",
            "Database migration",
            "Frontend component",
            "Background job processor",
            "Caching layer",
        ],
    )
    def test_various_feature_types(self, feature_type):
        """Test generation for various types of features."""
        result = run_generation(
            feature_description=f"Implement {feature_type}",
            additional_context="",
            dry_run=False,
        )

        assert isinstance(result, str)
        assert len(result) > 100
        assert feature_type.lower() in result.lower() or any(
            word in result.lower() for word in feature_type.lower().split()
        )


class TestAPIKeyValidation:
    """Test API key validation and error handling."""

    def test_missing_api_key_error(self):
        """Test that missing API key raises appropriate error."""
        # Temporarily remove API key
        original_key = settings.openrouter_api_key
        settings.openrouter_api_key = None

        try:
            with pytest.raises(ValueError, match="OPENROUTER_API_KEY is required"):
                run_generation(
                    feature_description="Test feature",
                    additional_context="",
                    dry_run=False,
                )
        finally:
            # Restore original key
            settings.openrouter_api_key = original_key

    def test_dry_run_bypasses_api_key_check(self):
        """Test that dry run mode works without API key."""
        # Temporarily remove API key
        original_key = settings.openrouter_api_key
        settings.openrouter_api_key = None

        try:
            result = run_generation(
                feature_description="Test feature", additional_context="", dry_run=True
            )

            assert isinstance(result, str)
            assert "[DRY RUN]" in result
        finally:
            # Restore original key
            settings.openrouter_api_key = original_key


# Pytest configuration for integration tests
def pytest_configure(config):
    """Configure pytest for integration tests."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
