[project]
name = "banana-forge"
version = "0.1.0"
description = "AI-powered CLI tool for generating structured feature implementation plans"
readme = "README.md"
authors = [
    { name = "<PERSON>", email = "dlk<PERSON><PERSON>@gmail.com" }
]
requires-python = ">=3.12"
dependencies = [
    "langchain>=0.3.26",
    "typer>=0.16.0",
    "pydantic[email]>=2.11.7",
    "openai>=1.97.0",
    "httpx>=0.28.1",
    "chromadb>=1.0.15",
    "pydantic-settings>=2.10.1",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.12.3",
    "pytest>=8.4.1",
    "black>=25.1.0",
    "mypy>=1.17.0",
]

[project.scripts]
banana-forge = "banana_forge:main"
bananaforge = "banana_forge:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.ruff]
target-version = "py312"
line-length = 88

[tool.ruff.lint]
select = ["E", "F", "I", "N", "W", "UP"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
