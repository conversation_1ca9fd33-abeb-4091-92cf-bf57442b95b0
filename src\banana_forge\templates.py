"""
Templates for Banana Forge feature implementation plans.

This module contains the template structure that guides the AI in generating
comprehensive feature implementation plans following the 10-agent format.
"""


def get_feature_template() -> str:
    """
    Get the standard feature implementation plan template.
    
    This template follows the 10-agent format described in the plan document,
    ensuring comprehensive coverage of all implementation aspects.
    
    Returns:
        str: The markdown template with placeholders
    """
    return """# {feature_name} Implementation Plan

*Generated on {timestamp}*

## Overview

Provide a comprehensive overview of the {feature_name} feature, including:
- Purpose and business value
- High-level technical approach
- Key components and integrations
- Success criteria

{additional_context}

## Current State Analysis

### Location in Codebase
- Identify relevant files, modules, and components
- Map existing functionality that relates to this feature
- Note any existing patterns or conventions to follow

### Existing Functionality
- Document current capabilities that this feature will extend or modify
- Identify potential conflicts or dependencies
- Note any technical debt that should be addressed

### Gap Analysis
- What's missing that this feature needs to provide
- What existing code needs to be modified or extended
- What new components need to be created

## Reference Standards

### Coding Guidelines
- Project-specific coding standards and conventions
- Language-specific best practices
- Architecture patterns in use

### Library Documentation
- Relevant external libraries and their usage patterns
- API documentation for integrations
- Framework-specific guidelines

### Best Practices
- Security considerations
- Performance requirements
- Testing standards
- Documentation requirements

## 10-Agent Parallelization Strategy

This implementation plan leverages a 10-agent approach to ensure comprehensive coverage:

| Agent Role | Focus Area | Key Responsibilities |
|------------|------------|---------------------|
| Core Implementation | Primary logic and algorithms | Main feature functionality, core business logic |
| Primary Operations | Standard workflows | Happy path implementations, primary user flows |
| Alternate Flows | Edge cases and variations | Alternative scenarios, conditional logic |
| Error Handling | Exception management | Error detection, recovery strategies, user feedback |
| Concurrency | Multi-threading and async | Thread safety, async operations, race conditions |
| Data Management | Storage and retrieval | Database design, caching, data validation |
| UI/UX Integration | User interface | Frontend components, user experience, accessibility |
| External Integration | Third-party services | API integrations, external dependencies |
| Monitoring & Logging | Observability | Metrics, logging, debugging, performance monitoring |
| Type Safety & Interfaces | Code contracts | Type definitions, interfaces, API contracts |

## Implementation Workflow

### Phase 1: Foundation (Week 1)
- [ ] Set up basic project structure
- [ ] Implement core data models
- [ ] Create basic API endpoints/interfaces
- [ ] Set up testing framework

### Phase 2: Core Functionality (Week 2-3)
- [ ] Implement primary business logic
- [ ] Add data persistence layer
- [ ] Create basic user interface
- [ ] Implement authentication/authorization

### Phase 3: Integration & Enhancement (Week 4)
- [ ] Add external service integrations
- [ ] Implement advanced features
- [ ] Add comprehensive error handling
- [ ] Performance optimization

### Phase 4: Testing & Deployment (Week 5)
- [ ] Comprehensive testing suite
- [ ] Documentation completion
- [ ] Deployment preparation
- [ ] Monitoring and logging setup

## Detailed Implementation Phases

### Core Implementation Agent
**Responsibilities:** Primary feature logic and algorithms

**Tasks:**
- Design and implement main business logic
- Create core algorithms and data processing
- Establish primary API contracts
- Implement fundamental operations

**Deliverables:**
- Core service classes
- Primary business logic functions
- Basic API structure
- Unit tests for core functionality

### Primary Operations Agent
**Responsibilities:** Standard user workflows

**Tasks:**
- Implement happy path user flows
- Create standard CRUD operations
- Design primary user interfaces
- Establish normal operation patterns

**Deliverables:**
- User workflow implementations
- Standard API endpoints
- Primary UI components
- Integration tests for main flows

### Alternate Flows Agent
**Responsibilities:** Edge cases and alternative scenarios

**Tasks:**
- Identify and implement edge cases
- Handle alternative user paths
- Manage conditional logic
- Support configuration variations

**Deliverables:**
- Edge case handling
- Alternative workflow implementations
- Configuration management
- Conditional logic tests

### Error Handling Agent
**Responsibilities:** Exception management and recovery

**Tasks:**
- Design error detection strategies
- Implement recovery mechanisms
- Create user-friendly error messages
- Establish logging for debugging

**Deliverables:**
- Exception handling framework
- Error recovery procedures
- User error feedback system
- Error logging and monitoring

### Concurrency Agent
**Responsibilities:** Thread safety and async operations

**Tasks:**
- Identify concurrency requirements
- Implement thread-safe operations
- Design async workflows
- Prevent race conditions

**Deliverables:**
- Thread-safe implementations
- Async operation handlers
- Concurrency tests
- Performance benchmarks

### Data Management Agent
**Responsibilities:** Storage, retrieval, and data integrity

**Tasks:**
- Design data models and schemas
- Implement data access layer
- Add caching strategies
- Ensure data validation

**Deliverables:**
- Database schema
- Data access objects
- Caching implementation
- Data validation rules

### UI/UX Integration Agent
**Responsibilities:** User interface and experience

**Tasks:**
- Design user interface components
- Implement responsive layouts
- Ensure accessibility compliance
- Create intuitive user flows

**Deliverables:**
- UI component library
- Responsive design implementation
- Accessibility features
- User experience documentation

### External Integration Agent
**Responsibilities:** Third-party services and APIs

**Tasks:**
- Integrate external APIs
- Handle service dependencies
- Implement fallback strategies
- Manage API rate limiting

**Deliverables:**
- External service integrations
- API client implementations
- Fallback mechanisms
- Integration monitoring

### Monitoring & Logging Agent
**Responsibilities:** Observability and debugging

**Tasks:**
- Implement comprehensive logging
- Add performance metrics
- Create monitoring dashboards
- Establish alerting systems

**Deliverables:**
- Logging framework
- Metrics collection
- Monitoring dashboards
- Alert configurations

### Type Safety & Interfaces Agent
**Responsibilities:** Code contracts and type definitions

**Tasks:**
- Define type interfaces
- Create API contracts
- Implement type validation
- Ensure type safety

**Deliverables:**
- Type definitions
- Interface specifications
- Type validation
- API documentation

## Validation Criteria

### Functional Requirements
- [ ] All specified features work as intended
- [ ] User workflows complete successfully
- [ ] Data integrity is maintained
- [ ] Performance meets requirements

### Technical Requirements
- [ ] Code follows project standards
- [ ] All tests pass with >90% coverage
- [ ] Security requirements are met
- [ ] Documentation is complete

### Quality Assurance
- [ ] Code review completed
- [ ] Security audit passed
- [ ] Performance testing completed
- [ ] User acceptance testing passed

## Risk Mitigation

### Technical Risks
- **Risk:** Integration complexity with existing systems
  - **Mitigation:** Incremental integration with thorough testing
  - **Contingency:** Fallback to manual processes if needed

- **Risk:** Performance impact on existing functionality
  - **Mitigation:** Performance testing and optimization
  - **Contingency:** Feature flags for gradual rollout

### Project Risks
- **Risk:** Timeline delays due to complexity
  - **Mitigation:** Phased implementation with MVP first
  - **Contingency:** Scope reduction if necessary

- **Risk:** Resource availability
  - **Mitigation:** Cross-training and documentation
  - **Contingency:** External contractor support

### Operational Risks
- **Risk:** User adoption challenges
  - **Mitigation:** User training and gradual rollout
  - **Contingency:** Enhanced support and documentation

- **Risk:** Data migration issues
  - **Mitigation:** Comprehensive testing and backup procedures
  - **Contingency:** Rollback plan and data recovery procedures

## Success Metrics

### Key Performance Indicators
- Feature adoption rate: Target >80% within 3 months
- User satisfaction score: Target >4.5/5
- Performance impact: <5% degradation in existing features
- Bug rate: <2 critical bugs per month

### Technical Metrics
- Code coverage: >90%
- Response time: <200ms for primary operations
- Uptime: >99.9%
- Security vulnerabilities: 0 critical, <5 medium

## Next Steps

1. **Immediate Actions (Next 1-2 days)**
   - Review and approve this implementation plan
   - Set up development environment
   - Create project tracking board

2. **Short-term Goals (Next week)**
   - Begin Phase 1 implementation
   - Set up CI/CD pipeline
   - Create initial documentation structure

3. **Medium-term Objectives (Next month)**
   - Complete core functionality
   - Conduct initial testing
   - Gather stakeholder feedback

4. **Long-term Vision (Next quarter)**
   - Full feature deployment
   - Performance optimization
   - Feature enhancement based on usage data"""


def get_simple_template() -> str:
    """
    Get a simplified template for quick generation.
    
    Returns:
        str: A simplified markdown template
    """
    return """# {feature_name} Implementation Plan

## Overview
{additional_context}

## Current State
{current_state}

## Implementation Approach
{implementation_phases}

## Validation
{validation_criteria}

## Risks & Mitigation
{risk_mitigation}

---
*Generated by Banana Forge*"""
