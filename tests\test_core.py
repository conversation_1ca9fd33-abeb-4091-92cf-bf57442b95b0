"""
Tests for the core functionality of Banana Forge.
"""

from unittest.mock import MagicMock, patch

import pytest

from banana_forge.core import _generate_dry_run_response, run_generation


class TestRunGeneration:
    """Test cases for the run_generation function."""

    def test_dry_run_mode(self):
        """Test that dry run mode returns a mock response."""
        feature = "Test Feature"
        result = run_generation(feature, dry_run=True)

        assert isinstance(result, str)
        assert feature in result
        assert "[DRY RUN]" in result
        assert "Implementation Plan" in result

    def test_dry_run_with_additional_context(self):
        """Test dry run mode with additional context."""
        feature = "Test Feature"
        context = "This is additional context for testing."
        result = run_generation(feature, additional_context=context, dry_run=True)

        assert isinstance(result, str)
        assert feature in result
        assert context in result
        assert "[DRY RUN]" in result

    @patch("banana_forge.core.LLMClient")
    @patch("banana_forge.core.settings")
    def test_generation_with_mock_llm(self, mock_settings, mock_llm_class):
        """Test generation with a mocked LLM client."""
        # Set up mock
        mock_llm = MagicMock()
        mock_llm.generate_completion.return_value = (
            "# Test Feature Implementation Plan\n\nThis is a test plan."
        )
        mock_llm_class.return_value = mock_llm

        # Mock settings
        mock_settings.validate_required_settings.return_value = None
        mock_settings.verbose = False
        mock_settings.primary_model = "test-model"

        result = run_generation("Test Feature")

        assert isinstance(result, str)
        assert "Test Feature Implementation Plan" in result
        assert "Generated by Banana Forge" in result
        mock_llm.generate_completion.assert_called_once()

    def test_empty_feature_description(self):
        """Test that empty feature description is handled."""
        result = run_generation("", dry_run=True)
        assert isinstance(result, str)
        assert "Implementation Plan" in result


class TestDryRunResponse:
    """Test cases for the dry run response generation."""

    def test_generate_dry_run_response(self):
        """Test the dry run response generation."""
        feature = "OAuth Integration"
        context = "Need to integrate with Google OAuth"

        result = _generate_dry_run_response(feature, context)

        assert isinstance(result, str)
        assert feature in result
        assert context in result
        assert "[DRY RUN]" in result
        assert "Implementation Plan" in result

    def test_generate_dry_run_response_no_context(self):
        """Test dry run response without additional context."""
        feature = "Payment System"

        result = _generate_dry_run_response(feature, "")

        assert isinstance(result, str)
        assert feature in result
        assert "No additional context provided" in result
        assert "[DRY RUN]" in result


class TestConfigurationValidation:
    """Test configuration validation in core functions."""

    @patch("banana_forge.core.LLMClient")
    @patch("banana_forge.core.settings")
    def test_missing_api_key_raises_error(self, mock_settings, mock_llm_class):
        """Test that missing API key raises appropriate error."""
        # Mock settings to raise validation error
        mock_settings.validate_required_settings.side_effect = ValueError(
            "API key missing"
        )

        with pytest.raises(ValueError, match="API key missing"):
            run_generation("Test Feature")

    @patch("banana_forge.core.settings")
    def test_dry_run_skips_validation(self, mock_settings):
        """Test that dry run mode skips configuration validation."""
        # This should not raise an error even if validation would fail
        mock_settings.validate_required_settings.side_effect = ValueError(
            "API key missing"
        )
        mock_settings.verbose = False

        result = run_generation("Test Feature", dry_run=True)
        assert isinstance(result, str)
        assert "[DRY RUN]" in result


if __name__ == "__main__":
    pytest.main([__file__])
