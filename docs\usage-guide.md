# Banana Forge Usage Guide

This comprehensive guide covers various usage scenarios and best practices for Banana Forge.

## Table of Contents

- [Getting Started](#getting-started)
- [Usage Scenarios](#usage-scenarios)
- [Best Practices](#best-practices)
- [Advanced Features](#advanced-features)
- [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

1. **Python 3.12+** installed on your system
2. **uv** package manager (recommended) or pip
3. **OpenRouter API key** for AI model access
4. **Ollama** (optional) for local model support

### Quick Setup

```bash
# Clone and setup
git clone <repository-url>
cd banana-forge
uv sync

# Configure API keys
cp .env.example .env
# Edit .env with your OpenRouter API key

# Test installation
uv run banana-forge --help
uv run banana-forge generate "Test feature" --dry-run
```

## Usage Scenarios

### 1. Planning a New Feature

**Scenario:** You need to implement a new user authentication system.

```bash
# Basic generation
uv run banana-forge generate "User authentication with JWT tokens"

# With additional context
uv run banana-forge generate "User authentication with JWT tokens" \
  --input auth_requirements.txt \
  --output auth_implementation_plan.md \
  --verbose
```

**Expected Output:** A comprehensive plan covering:
- Current state analysis
- Implementation phases
- Security considerations
- Testing strategies
- Risk mitigation

### 2. API Development Planning

**Scenario:** Building a REST API for a mobile app.

```bash
# Create context file first
cat > api_context.txt << EOF
Technology Stack:
- FastAPI framework
- PostgreSQL database
- Redis for caching
- JWT authentication
- Docker deployment

Requirements:
- User management endpoints
- Real-time notifications
- File upload capabilities
- Rate limiting
- API versioning
EOF

# Generate plan
uv run banana-forge generate "Mobile app REST API with real-time features" \
  --input api_context.txt \
  --output mobile_api_plan.md
```

### 3. Database Migration Planning

**Scenario:** Planning a complex database schema change.

```bash
uv run banana-forge generate "Migrate user table to support multi-tenancy" \
  --input current_schema.sql \
  --verbose
```

### 4. Frontend Component Planning

**Scenario:** Building a complex React component.

```bash
# Create component requirements
echo "Interactive data visualization dashboard with:
- Real-time chart updates
- Multiple chart types (line, bar, pie)
- Filtering and search capabilities
- Export functionality
- Responsive design" > dashboard_requirements.txt

uv run banana-forge generate "React dashboard component with data visualization" \
  --input dashboard_requirements.txt \
  --output dashboard_plan.md
```

### 5. Microservice Architecture Planning

**Scenario:** Breaking down a monolith into microservices.

```bash
uv run banana-forge generate "Extract payment processing into microservice" \
  --input monolith_analysis.md \
  --verbose
```

## Best Practices

### 1. Providing Context

**Good Context Examples:**
```bash
# Include technology stack
echo "Tech stack: Node.js, Express, MongoDB, React" > context.txt

# Include existing patterns
echo "Follow existing error handling patterns in src/utils/errors.js" >> context.txt

# Include constraints
echo "Must maintain backward compatibility with v1 API" >> context.txt
```

### 2. Feature Description Guidelines

**Effective Feature Descriptions:**
- Be specific: "OAuth2 login with Google and GitHub" vs "login system"
- Include scope: "User profile management with avatar upload and preferences"
- Mention integrations: "Payment processing with Stripe and PayPal support"

**Examples:**
```bash
# Good: Specific and scoped
uv run banana-forge generate "Real-time chat with WebSocket, message history, and file sharing"

# Better: Includes technical details
uv run banana-forge generate "GraphQL API with subscription support for real-time updates and caching"

# Best: Comprehensive with context file
uv run banana-forge generate "Multi-tenant SaaS billing system" --input billing_requirements.md
```

### 3. Output Management

```bash
# Organize outputs by feature
mkdir -p plans/features
uv run banana-forge generate "User onboarding flow" -o plans/features/onboarding.md

# Use timestamps for iterations
uv run banana-forge generate "Payment integration v2" -o "plans/payment_$(date +%Y%m%d).md"

# Keep context files with plans
cp requirements.txt plans/features/onboarding_context.txt
```

## Advanced Features

### 1. Code Indexing for Context

```bash
# Index your codebase for better context
uv run banana-forge index --path ./src --verbose

# Search indexed code
uv run banana-forge search "authentication patterns"

# Check indexing status
uv run banana-forge db-stats
```

### 2. Configuration Management

```bash
# Check current configuration
uv run banana-forge config

# Use different models
export PRIMARY_MODEL="anthropic/claude-3-sonnet"
uv run banana-forge generate "Complex feature"

# Adjust concurrency for performance
export MAX_CONCURRENT_AGENTS=3
```

### 3. Integration with Development Workflow

```bash
# Pre-commit hook for feature planning
cat > .git/hooks/pre-feature << 'EOF'
#!/bin/bash
if [ "$1" = "feature" ]; then
    echo "Generating implementation plan..."
    uv run banana-forge generate "$2" -o "plans/$(echo $2 | tr ' ' '_').md"
fi
EOF
chmod +x .git/hooks/pre-feature
```

## Troubleshooting

### Common Issues and Solutions

#### 1. API Key Issues
```bash
# Verify API key is set
uv run banana-forge config

# Test with dry run first
uv run banana-forge generate "test" --dry-run

# Check API key validity
curl -H "Authorization: Bearer $OPENROUTER_API_KEY" https://openrouter.ai/api/v1/models
```

#### 2. Model Issues
```bash
# List available models
uv run python -c "from banana_forge.llm import LLMClient; print(LLMClient().list_available_models())"

# Test model health
uv run python -c "from banana_forge.llm import LLMClient; print(LLMClient().health_check())"

# Use alternative model
export PRIMARY_MODEL="openai/gpt-4"
```

#### 3. Performance Issues
```bash
# Reduce concurrent agents
export MAX_CONCURRENT_AGENTS=2

# Use local model for faster responses
export PRIMARY_MODEL="qwen2.5:8b"

# Clear and rebuild index
uv run banana-forge index --reset
```

#### 4. Output Quality Issues
```bash
# Provide more context
echo "Detailed requirements and constraints" > context.txt
uv run banana-forge generate "feature" --input context.txt

# Use verbose mode for debugging
uv run banana-forge generate "feature" --verbose

# Try different model
export PRIMARY_MODEL="anthropic/claude-3-opus"
```

### Debug Mode

```bash
# Enable maximum verbosity
export LOG_LEVEL=DEBUG
export VERBOSE=true
uv run banana-forge generate "feature" --verbose
```

### Getting Help

1. **Check logs:** Look in the console output with `--verbose`
2. **Test components:** Use individual commands to isolate issues
3. **Verify setup:** Run `uv run banana-forge config` to check configuration
4. **Use dry run:** Test without API calls using `--dry-run`
5. **Check documentation:** Review this guide and the main README

## Tips for Better Results

1. **Be Specific:** Include technical details and constraints
2. **Provide Context:** Use input files with requirements and existing patterns
3. **Iterate:** Generate multiple plans and compare approaches
4. **Validate:** Review generated plans for accuracy and completeness
5. **Customize:** Adjust models and settings based on your needs

## Next Steps

- Explore the [API documentation](api-reference.md)
- Learn about [Phase 2 features](../docs/banana-forge-plan.md#phase-2-multi-agent-orchestration--parallelization)
- Contribute to the project on GitHub
