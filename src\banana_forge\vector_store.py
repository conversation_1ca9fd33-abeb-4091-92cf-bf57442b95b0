"""
Vector store management for Banana Forge using ChromaDB.

This module handles indexing and searching code files using ChromaDB
for semantic similarity search during feature plan generation.
"""

import logging
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import hashlib

import chromadb
from chromadb.config import Settings as ChromaSettings

from .config import settings

logger = logging.getLogger(__name__)


class CodeVectorStore:
    """
    Manages a ChromaDB vector store for code and documentation files.
    
    Provides functionality to index project files and perform semantic
    search to find relevant code snippets for feature implementation.
    """
    
    def __init__(self):
        """Initialize the ChromaDB client and collection."""
        self.client = None
        self.collection = None
        self._setup_client()
    
    def _setup_client(self) -> None:
        """Set up the ChromaDB client and collection."""
        try:
            # Ensure the ChromaDB directory exists
            chroma_path = settings.ensure_chroma_db_path()
            
            # Initialize ChromaDB client
            self.client = chromadb.PersistentClient(
                path=str(chroma_path),
                settings=ChromaSettings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Get or create collection
            self.collection = self.client.get_or_create_collection(
                name=settings.chroma_collection_name,
                metadata={"description": "Banana Forge code and documentation store"}
            )
            
            if settings.verbose:
                logger.info(f"ChromaDB initialized at {chroma_path}")
                logger.info(f"Collection '{settings.chroma_collection_name}' ready")
                
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB: {e}")
            raise
    
    def index_project_files(self, project_root: Optional[Path] = None) -> Dict[str, Any]:
        """
        Index all relevant files in the project for semantic search.
        
        Args:
            project_root: Root directory to index (defaults to current project)
            
        Returns:
            Dict with indexing statistics
        """
        if project_root is None:
            project_root = settings.get_project_root()
        
        logger.info(f"Starting to index project files from {project_root}")
        
        # File patterns to include
        include_patterns = [
            "*.py", "*.js", "*.ts", "*.jsx", "*.tsx", "*.java", "*.cpp", "*.c",
            "*.h", "*.hpp", "*.cs", "*.go", "*.rs", "*.rb", "*.php", "*.swift",
            "*.kt", "*.scala", "*.clj", "*.hs", "*.ml", "*.r", "*.sql",
            "*.md", "*.rst", "*.txt", "*.yaml", "*.yml", "*.json", "*.toml",
            "*.cfg", "*.ini", "*.conf"
        ]
        
        # Directories to exclude
        exclude_dirs = {
            ".git", ".venv", "venv", "env", "__pycache__", ".pytest_cache",
            "node_modules", ".next", "dist", "build", "target", ".idea",
            ".vscode", "chroma_db", ".chroma", "logs", "tmp", "temp"
        }
        
        files_processed = 0
        files_indexed = 0
        errors = []
        
        for pattern in include_patterns:
            for file_path in project_root.rglob(pattern):
                # Skip if in excluded directory
                if any(excluded in file_path.parts for excluded in exclude_dirs):
                    continue
                
                # Skip if file is too large (>1MB)
                if file_path.stat().st_size > 1024 * 1024:
                    continue
                
                try:
                    files_processed += 1
                    if self._index_file(file_path, project_root):
                        files_indexed += 1
                        
                    if files_processed % 50 == 0:
                        logger.info(f"Processed {files_processed} files...")
                        
                except Exception as e:
                    error_msg = f"Error indexing {file_path}: {e}"
                    errors.append(error_msg)
                    logger.warning(error_msg)
        
        stats = {
            "files_processed": files_processed,
            "files_indexed": files_indexed,
            "errors": len(errors),
            "collection_count": self.collection.count()
        }
        
        logger.info(f"Indexing complete: {stats}")
        return stats
    
    def _index_file(self, file_path: Path, project_root: Path) -> bool:
        """
        Index a single file into the vector store.
        
        Args:
            file_path: Path to the file to index
            project_root: Root directory of the project
            
        Returns:
            bool: True if file was indexed, False if skipped
        """
        try:
            # Read file content
            content = file_path.read_text(encoding='utf-8', errors='ignore')
            
            # Skip empty files
            if not content.strip():
                return False
            
            # Create relative path for cleaner references
            relative_path = file_path.relative_to(project_root)
            
            # Create document ID based on file path and content hash
            content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
            doc_id = f"{relative_path}#{content_hash}"
            
            # Check if this version is already indexed
            try:
                existing = self.collection.get(ids=[doc_id])
                if existing['ids']:
                    return False  # Already indexed
            except:
                pass  # Document doesn't exist, continue with indexing
            
            # Split content into chunks for better search granularity
            chunks = self._split_content(content, file_path)
            
            # Index each chunk
            for i, chunk in enumerate(chunks):
                chunk_id = f"{doc_id}#chunk{i}"
                
                # Prepare metadata
                metadata = {
                    "file_path": str(relative_path),
                    "file_type": file_path.suffix,
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "file_size": len(content),
                    "chunk_size": len(chunk)
                }
                
                # Add to collection
                self.collection.add(
                    documents=[chunk],
                    metadatas=[metadata],
                    ids=[chunk_id]
                )
            
            return True
            
        except Exception as e:
            logger.warning(f"Failed to index {file_path}: {e}")
            return False
    
    def _split_content(self, content: str, file_path: Path) -> List[str]:
        """
        Split file content into searchable chunks.
        
        Args:
            content: File content to split
            file_path: Path to the file (for context)
            
        Returns:
            List of content chunks
        """
        # For code files, try to split by functions/classes
        if file_path.suffix in ['.py', '.js', '.ts', '.java', '.cpp', '.c']:
            return self._split_code_content(content)
        
        # For other files, split by paragraphs or lines
        return self._split_text_content(content)
    
    def _split_code_content(self, content: str) -> List[str]:
        """Split code content into logical chunks."""
        lines = content.split('\n')
        chunks = []
        current_chunk = []
        chunk_size = 0
        max_chunk_size = 1000  # characters
        
        for line in lines:
            current_chunk.append(line)
            chunk_size += len(line) + 1  # +1 for newline
            
            # Split on function/class definitions or when chunk gets too large
            if (chunk_size > max_chunk_size or 
                (line.strip().startswith(('def ', 'class ', 'function ', 'const ')) and 
                 len(current_chunk) > 1)):
                
                if len(current_chunk) > 1:
                    chunks.append('\n'.join(current_chunk[:-1]))
                    current_chunk = [line]
                    chunk_size = len(line) + 1
        
        # Add remaining content
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    def _split_text_content(self, content: str) -> List[str]:
        """Split text content into logical chunks."""
        # Split by double newlines (paragraphs) first
        paragraphs = content.split('\n\n')
        chunks = []
        current_chunk = []
        chunk_size = 0
        max_chunk_size = 1000
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
                
            if chunk_size + len(paragraph) > max_chunk_size and current_chunk:
                chunks.append('\n\n'.join(current_chunk))
                current_chunk = [paragraph]
                chunk_size = len(paragraph)
            else:
                current_chunk.append(paragraph)
                chunk_size += len(paragraph) + 2  # +2 for \n\n
        
        if current_chunk:
            chunks.append('\n\n'.join(current_chunk))
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    def search(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """
        Search for relevant code snippets based on a query.
        
        Args:
            query: Search query describing what to look for
            n_results: Maximum number of results to return
            
        Returns:
            List of search results with content and metadata
        """
        try:
            if not self.collection:
                logger.warning("ChromaDB collection not initialized")
                return []
            
            # Perform semantic search
            results = self.collection.query(
                query_texts=[query],
                n_results=min(n_results, settings.max_code_snippets)
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    result = {
                        'content': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'distance': results['distances'][0][i] if results['distances'] else None,
                        'id': results['ids'][0][i] if results['ids'] else None
                    }
                    formatted_results.append(result)
            
            if settings.verbose:
                logger.info(f"Found {len(formatted_results)} results for query: {query}")
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the current collection."""
        try:
            if not self.collection:
                return {"error": "Collection not initialized"}
            
            count = self.collection.count()
            
            # Get sample of documents to analyze
            sample = self.collection.peek(limit=10)
            
            file_types = {}
            if sample['metadatas']:
                for metadata in sample['metadatas']:
                    file_type = metadata.get('file_type', 'unknown')
                    file_types[file_type] = file_types.get(file_type, 0) + 1
            
            return {
                "total_documents": count,
                "collection_name": settings.chroma_collection_name,
                "sample_file_types": file_types,
                "database_path": str(settings.chroma_db_path)
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {"error": str(e)}
    
    def reset_collection(self) -> bool:
        """Reset the collection (delete all documents)."""
        try:
            if self.collection:
                self.client.delete_collection(settings.chroma_collection_name)
                self.collection = self.client.create_collection(
                    name=settings.chroma_collection_name,
                    metadata={"description": "Banana Forge code and documentation store"}
                )
                logger.info("Collection reset successfully")
                return True
        except Exception as e:
            logger.error(f"Failed to reset collection: {e}")
            return False


# Global instance
vector_store = CodeVectorStore()


def get_vector_store() -> CodeVectorStore:
    """Get the global vector store instance."""
    return vector_store
