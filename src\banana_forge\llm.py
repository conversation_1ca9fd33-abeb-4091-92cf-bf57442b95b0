"""
LLM client for Banana Forge.

This module provides a unified interface for interacting with different LLM providers,
including OpenRouter (for remote models) and Ollama (for local models).
"""

import logging
from typing import Any

import httpx
from openai import OpenAI

from .config import settings

logger = logging.getLogger(__name__)


class LLMClient:
    """
    Unified client for interacting with different LLM providers.
    
    Supports both OpenRouter (for remote models like Kimi K2) and Ollama
    (for local models like Qwen-3).
    """

    def __init__(self):
        """Initialize the LLM client with configured providers."""
        self._openrouter_client: OpenAI | None = None
        self._ollama_client: httpx.Client | None = None
        self._setup_clients()

    def _setup_clients(self) -> None:
        """Set up the OpenRouter and Ollama clients."""
        # Set up OpenRouter client
        if settings.openrouter_api_key:
            self._openrouter_client = OpenAI(
                api_key=settings.openrouter_api_key,
                base_url=settings.openrouter_base_url,
            )
            if settings.verbose:
                logger.info("OpenRouter client initialized")
        else:
            logger.warning("OpenRouter API key not provided - remote models unavailable")

        # Set up Ollama client
        self._ollama_client = httpx.Client(
            base_url=settings.ollama_base_url,
            timeout=300.0,  # 5 minutes timeout for model responses
        )
        if settings.verbose:
            logger.info(f"Ollama client initialized for {settings.ollama_base_url}")

    def generate_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int = 4000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """
        Generate a completion using the specified model.
        
        Args:
            prompt: The input prompt
            model: Model name (e.g., "moonshot/kimi-k2" or "qwen2.5:8b")
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional model-specific parameters
            
        Returns:
            str: The generated completion
            
        Raises:
            ValueError: If model is not available or invalid
            Exception: If generation fails
        """
        if settings.verbose:
            logger.info(f"Generating completion with model: {model}")
            logger.debug(f"Prompt length: {len(prompt)} characters")

        # Determine if this is a local or remote model
        if self._is_local_model(model):
            return self._generate_ollama_completion(
                prompt=prompt,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
        else:
            return self._generate_openrouter_completion(
                prompt=prompt,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )

    def _is_local_model(self, model: str) -> bool:
        """Check if a model should be handled by Ollama (local)."""
        # Local models typically don't have a "/" in the name or are in our known local models
        local_indicators = ["qwen", "llama", "mistral", "phi", "gemma"]
        return (
            "/" not in model or
            any(indicator in model.lower() for indicator in local_indicators)
        )

    def _generate_openrouter_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        **kwargs
    ) -> str:
        """Generate completion using OpenRouter."""
        if not self._openrouter_client:
            raise ValueError("OpenRouter client not available - check API key configuration")

        try:
            response = self._openrouter_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )

            content = response.choices[0].message.content
            if not content:
                raise ValueError("Empty response from OpenRouter")

            if settings.verbose:
                logger.info(f"OpenRouter completion generated: {len(content)} characters")
                if hasattr(response, 'usage'):
                    logger.info(f"Token usage: {response.usage}")

            return content

        except Exception as e:
            logger.error(f"OpenRouter completion failed: {e}")
            raise

    def _generate_ollama_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        **kwargs
    ) -> str:
        """Generate completion using Ollama."""
        if not self._ollama_client:
            raise ValueError("Ollama client not available")

        try:
            # Prepare Ollama request
            request_data = {
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": temperature,
                    **kwargs
                }
            }

            response = self._ollama_client.post("/api/generate", json=request_data)
            response.raise_for_status()

            result = response.json()
            content = result.get("response", "")

            if not content:
                raise ValueError("Empty response from Ollama")

            if settings.verbose:
                logger.info(f"Ollama completion generated: {len(content)} characters")
                if "eval_count" in result:
                    logger.info(f"Tokens generated: {result['eval_count']}")

            return content

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                raise ValueError(f"Model '{model}' not found in Ollama. Please pull the model first.")
            else:
                logger.error(f"Ollama HTTP error: {e}")
                raise
        except Exception as e:
            logger.error(f"Ollama completion failed: {e}")
            raise

    def list_available_models(self) -> dict[str, Any]:
        """
        List available models from both providers.
        
        Returns:
            Dict containing available models from each provider
        """
        models = {
            "openrouter": [],
            "ollama": []
        }

        # Get OpenRouter models (if available)
        if self._openrouter_client:
            try:
                # Note: OpenRouter doesn't have a standard models endpoint
                # This would need to be implemented based on their API
                models["openrouter"] = ["moonshot/kimi-k2", "anthropic/claude-3-sonnet"]
            except Exception as e:
                logger.warning(f"Could not fetch OpenRouter models: {e}")

        # Get Ollama models
        try:
            response = self._ollama_client.get("/api/tags")
            response.raise_for_status()
            ollama_models = response.json()
            models["ollama"] = [model["name"] for model in ollama_models.get("models", [])]
        except Exception as e:
            logger.warning(f"Could not fetch Ollama models: {e}")

        return models

    def health_check(self) -> dict[str, bool]:
        """
        Check the health of both LLM providers.
        
        Returns:
            Dict indicating the health status of each provider
        """
        health = {
            "openrouter": False,
            "ollama": False
        }

        # Check OpenRouter
        if self._openrouter_client:
            try:
                # Simple test call
                self._openrouter_client.chat.completions.create(
                    model=settings.primary_model,
                    messages=[{"role": "user", "content": "Hello"}],
                    max_tokens=1
                )
                health["openrouter"] = True
            except Exception as e:
                logger.warning(f"OpenRouter health check failed: {e}")

        # Check Ollama
        try:
            response = self._ollama_client.get("/api/tags")
            health["ollama"] = response.status_code == 200
        except Exception as e:
            logger.warning(f"Ollama health check failed: {e}")

        return health
