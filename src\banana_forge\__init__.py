"""
Banana Forge - AI-powered CLI tool for generating structured feature implementation plans.

This package provides a comprehensive solution for generating detailed feature
implementation plans using AI agents and structured templates.
"""

__version__ = "0.1.0"
__author__ = "<PERSON>"
__email__ = "dlk<PERSON><PERSON>@gmail.com"

from .config import settings
from .core import run_generation

__all__ = ["main", "run_generation", "settings"]


def main() -> None:
    """Main entry point for the CLI application."""
    from .cli import main as cli_main

    cli_main()
